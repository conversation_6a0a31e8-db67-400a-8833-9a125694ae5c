/**
 * History State Composable
 *
 * Manages the state and logic for conversation history
 * 使用单例模式确保状态在组件间共享
 */
import { ref, reactive, computed } from 'vue';
import { fetchHistory, fetchConversation, deleteConversation } from '../services/historyService.js';

// 在模块级别创建状态，确保单例
// State
const isLoading = ref(false);
const error = ref(null);
const conversationGroups = ref([]);
const activeConversationId = ref(null);
const currentPage = ref(1);
const pageSize = ref(20);
const totalCount = ref(0);
const rawHistoryData = ref({});

// Computed
const hasMorePages = computed(() => {
    const result = currentPage.value * pageSize.value < totalCount.value;
    console.log('hasMorePages computed:', {
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        totalCount: totalCount.value,
        hasMorePages: result
    });
    return result;
});

// Delete confirmation modal state
const deleteConfirmModal = reactive({
    isOpen: false,
    conversationId: null,
    title: ''
});

// Date helpers
const getDateBefore = (days) => {
    const date = new Date();
    date.setDate(date.getDate() - days);
    return date.toISOString().split('T')[0];
};

const today = new Date().toISOString().split('T')[0];
const yesterday = getDateBefore(1);

// Group conversations by date
const groupConversationsByDate = (conversations) => {
    if (!conversations || Object.keys(conversations).length === 0) {
        return [];
    }

    const groups = [
        { id: 'today', title: '今天', conversations: [] },
        { id: 'yesterday', title: '昨天', conversations: [] },
        { id: 'past_week', title: '过去 7 天', conversations: [] },
        { id: 'past_month', title: '过去 30 天', conversations: [] },
        { id: 'older', title: '更早', conversations: [] }
    ];

    // Process the conversations data structure
    // The API returns an object with conversation_ids as keys and arrays of messages as values
    Object.entries(conversations).forEach(([conversationId, messages]) => {
        if (!messages || messages.length === 0) return;

        // Get the last message to determine timestamp
        const lastMessage = messages[messages.length - 1];

        // Use the timestamp from the last message for sorting
        const timestamp = lastMessage.timestamp;
        // Convert timestamp from milliseconds to date
        const date = new Date(timestamp).toISOString().split('T')[0];

        // Create a title from the first user message, or use a default
        let title = '未命名对话';
        for (const msg of messages) {
            if (msg.role === 'user') {
                title = msg.content;
                if (title.length > 30) {
                    title = title.substring(0, 30) + '...';
                }
                break;
            }
        }

        // Create conversation object
        const conversation = {
            id: conversationId,
            title: title,
            date: date,
            timestamp: timestamp,
            active: conversationId === activeConversationId.value,
            // 保留反馈信息（从第一条消息中获取，因为反馈是对话级别的）
            isGoodCase: messages[0]?.isGoodCase || false,
            isBadCase: messages[0]?.isBadCase || false,
            goodCaseFeedback: messages[0]?.goodCaseFeedback || null,
            badCaseFeedback: messages[0]?.badCaseFeedback || null
        };

        // Determine which group to add to
        if (date === today) {
            groups[0].conversations.push(conversation);
        } else if (date === yesterday) {
            groups[1].conversations.push(conversation);
        } else {
            const daysDiff = Math.floor((new Date(today) - new Date(date)) / (1000 * 60 * 60 * 24));

            if (daysDiff <= 7) {
                groups[2].conversations.push(conversation);
            } else if (daysDiff <= 30) {
                groups[3].conversations.push(conversation);
            } else {
                groups[4].conversations.push(conversation);
            }
        }
    });

    // Sort conversations within each group by timestamp (newest first)
    groups.forEach(group => {
        group.conversations.sort((a, b) => b.timestamp - a.timestamp);
    });

    // Only return groups with conversations
    return groups.filter(group => group.conversations.length > 0);
};

// Merge new conversation groups into existing ones
const mergeConversationGroups = (newGroups) => {
    newGroups.forEach(newGroup => {
        const existingGroup = conversationGroups.value.find(g => g.id === newGroup.id);

        if (existingGroup) {
            // Merge conversations, avoiding duplicates
            newGroup.conversations.forEach(conv => {
                if (!existingGroup.conversations.some(c => c.id === conv.id)) {
                    existingGroup.conversations.push(conv);
                }
            });
        } else {
            // If group doesn't exist, add the new group
            conversationGroups.value.push(newGroup);
        }
    });
};

// Load history with pagination
const loadHistory = async (page = 1) => {
    try {
        isLoading.value = true;
        error.value = null;

        const result = await fetchHistory(page, pageSize.value);
        totalCount.value = result.total_count;

        // Store the raw history data
        if (page === 1) {
            // If first page, replace the data
            rawHistoryData.value = result.history || {};
        } else {
            // Otherwise, merge the new data
            rawHistoryData.value = { ...rawHistoryData.value, ...(result.history || {}) };
        }

        // If first page, reset groups
        if (page === 1) {
            conversationGroups.value = groupConversationsByDate(result.history);
        } else {
            // Otherwise, merge new conversations into existing groups
            const newGroups = groupConversationsByDate(result.history);
            mergeConversationGroups(newGroups);
        }

        currentPage.value = page;
        return result;
    } catch (err) {
        error.value = err.message || '获取历史记录失败';
        console.error('加载历史记录失败:', err);
        return null;
    } finally {
        isLoading.value = false;
    }
};

// Load more history (for infinite scroll)
const loadMoreHistory = () => {
    if (!isLoading.value && hasMorePages.value) {
        loadHistory(currentPage.value + 1);
    }
};

// 加载单个对话内容
// 直接从API获取单个对话，而不需要加载所有历史记录
const loadSingleConversation = async (conversationId) => {
    if (!conversationId) return null;

    try {
        isLoading.value = true;
        error.value = null;

        // 调用API获取单个对话内容
        const messages = await fetchConversation(conversationId);

        if (messages && messages.length > 0) {
            // 创建一个新的条目，以对话ID为键
            rawHistoryData.value[conversationId] = messages;

            // 设置为活动对话
            activeConversationId.value = conversationId;

            // 创建对话对象，用于添加到分组中
            const lastMessage = messages[messages.length - 1];
            const timestamp = lastMessage.timestamp;
            const date = new Date(timestamp).toISOString().split('T')[0];

            // 创建标题
            let title = '未命名对话';
            for (const msg of messages) {
                if (msg.role === 'user') {
                    title = msg.content;
                    if (title.length > 30) {
                        title = title.substring(0, 30) + '...';
                    }
                    break;
                }
            }

            // 创建对话对象，包含反馈信息
            const firstMessage = messages[0];
            const conversation = {
                id: conversationId,
                title: title,
                date: date,
                timestamp: timestamp,
                active: true,
                isGoodCase: firstMessage?.isGoodCase || false,
                isBadCase: firstMessage?.isBadCase || false,
                goodCaseFeedback: firstMessage?.goodCaseFeedback || null,
                badCaseFeedback: firstMessage?.badCaseFeedback || null
            };

            // 确定应该添加到哪个分组
            const today = new Date().toISOString().split('T')[0];
            const yesterday = getDateBefore(1);

            let targetGroup;
            if (date === today) {
                // 查找或创建"今天"分组
                targetGroup = conversationGroups.value.find(g => g.id === 'today');
                if (!targetGroup) {
                    targetGroup = { id: 'today', title: '今天', conversations: [] };
                    conversationGroups.value.unshift(targetGroup);
                }
            } else if (date === yesterday) {
                // 查找或创建"昨天"分组
                targetGroup = conversationGroups.value.find(g => g.id === 'yesterday');
                if (!targetGroup) {
                    targetGroup = { id: 'yesterday', title: '昨天', conversations: [] };
                    // 如果存在"今天"分组，则插入到其后
                    const todayIndex = conversationGroups.value.findIndex(g => g.id === 'today');
                    if (todayIndex >= 0) {
                        conversationGroups.value.splice(todayIndex + 1, 0, targetGroup);
                    } else {
                        conversationGroups.value.unshift(targetGroup);
                    }
                }
            } else {
                // 计算日期差
                const daysDiff = Math.floor((new Date(today) - new Date(date)) / (1000 * 60 * 60 * 24));

                if (daysDiff <= 7) {
                    // 查找或创建"过去7天"分组
                    targetGroup = conversationGroups.value.find(g => g.id === 'past_week');
                    if (!targetGroup) {
                        targetGroup = { id: 'past_week', title: '过去 7 天', conversations: [] };
                        conversationGroups.value.push(targetGroup);
                    }
                } else if (daysDiff <= 30) {
                    // 查找或创建"过去30天"分组
                    targetGroup = conversationGroups.value.find(g => g.id === 'past_month');
                    if (!targetGroup) {
                        targetGroup = { id: 'past_month', title: '过去 30 天', conversations: [] };
                        conversationGroups.value.push(targetGroup);
                    }
                } else {
                    // 查找或创建"更早"分组
                    targetGroup = conversationGroups.value.find(g => g.id === 'older');
                    if (!targetGroup) {
                        targetGroup = { id: 'older', title: '更早', conversations: [] };
                        conversationGroups.value.push(targetGroup);
                    }
                }
            }

            // 将对话添加到目标分组
            // 检查是否已存在相同ID的对话
            const existingIndex = targetGroup.conversations.findIndex(c => c.id === conversationId);
            if (existingIndex >= 0) {
                // 更新现有对话
                targetGroup.conversations[existingIndex] = conversation;
            } else {
                // 添加新对话到分组的最前面
                targetGroup.conversations.unshift(conversation);
            }

            // 更新所有对话的活动状态
            conversationGroups.value.forEach(group => {
                group.conversations.forEach(conv => {
                    conv.active = conv.id === conversationId;
                });
            });

            return messages;
        }

        return null;
    } catch (err) {
        error.value = err.message || '获取对话内容失败';
        console.error('加载单个对话失败:', err);
        return null;
    } finally {
        isLoading.value = false;
    }
};

// Select a conversation
const selectConversation = (conversationId) => {
    // 检查对话ID是否存在于原始历史数据中
    if (!rawHistoryData.value[conversationId]) {
        // 直接从API获取单个对话内容，而不是加载所有历史记录
        loadSingleConversation(conversationId).then(messages => {
            if (!messages || messages.length === 0) {
                console.warn(`无法通过API加载对话 ${conversationId}，尝试回退到加载历史记录方法...`);
                // 如果API获取失败，回退到旧方法
                loadHistory(1).then(() => {
                    // 再次检查对话ID是否存在
                    if (rawHistoryData.value[conversationId]) {
                        // 递归调用自身，此时应该能找到对话ID
                        selectConversation(conversationId);
                    } else {
                        console.warn(`对话 ${conversationId} 在历史记录中未找到`);
                    }
                });
            }
            // 如果成功加载，不需要做任何事情，因为 loadSingleConversation 已经设置了活动对话
        });
        return;
    }

    // Update active conversation ID
    activeConversationId.value = conversationId;

    // 使用查询参数而不是哈希值来表示当前活动的对话
    // Use query parameter instead of hash to represent the active conversation
    const url = new URL(window.location.href);
    url.searchParams.set('chat', conversationId);
    // 使用 history.pushState 更新 URL 而不刷新页面
    window.history.pushState({}, '', url);

    // Update active state of all conversations
    conversationGroups.value.forEach(group => {
        group.conversations.forEach(conv => {
            conv.active = conv.id === conversationId;
        });
    });
};

// Open delete confirmation modal
const openDeleteConfirmModal = (id, title) => {
    deleteConfirmModal.isOpen = true;
    deleteConfirmModal.conversationId = id;
    deleteConfirmModal.title = title;
};

// Close delete confirmation modal
const closeDeleteConfirmModal = () => {
    deleteConfirmModal.isOpen = false;
    deleteConfirmModal.conversationId = null;
    deleteConfirmModal.title = '';
};

// Confirm and execute conversation deletion
const confirmDeleteConversation = async () => {
    if (!deleteConfirmModal.conversationId) return;

    try {
        await deleteConversation(deleteConfirmModal.conversationId);

        // Remove deleted conversation from groups
        conversationGroups.value.forEach(group => {
            group.conversations = group.conversations.filter(
                conv => conv.id !== deleteConfirmModal.conversationId
            );
        });

        // Remove empty groups
        conversationGroups.value = conversationGroups.value.filter(
            group => group.conversations.length > 0
        );

        // If deleted conversation was active, clear active conversation
        if (activeConversationId.value === deleteConfirmModal.conversationId) {
            activeConversationId.value = null;
        }

        // Close modal
        closeDeleteConfirmModal();
    } catch (err) {
        console.error('删除会话失败:', err);
        // Error handling could be improved here
    }
};

// Set active conversation ID (用于新建对话时设置预生成的ID)
const setActiveConversationId = (conversationId) => {
    activeConversationId.value = conversationId;
    console.log('=== 设置活动对话ID ===', conversationId);
};

// Create a new conversation (reset active conversation)
const newConversation = () => {
    // Clear active conversation (如果没有预设的ID)
    if (!activeConversationId.value) {
        activeConversationId.value = null;
    }

    // 使用查询参数而不是哈希值来表示新对话状态
    // Use query parameter instead of hash to represent new conversation state
    const url = new URL(window.location.href);
    // 移除chat参数
    url.searchParams.delete('chat');
    // 使用 history.pushState 更新 URL 而不刷新页面
    window.history.pushState({}, '', url);

    // Update all conversations to inactive
    conversationGroups.value.forEach(group => {
        group.conversations.forEach(conv => {
            conv.active = false;
        });
    });
};

// Get messages for a specific conversation
const getConversationMessages = (conversationId) => {
    const messages = rawHistoryData.value[conversationId] || [];

    // 确保消息包含反馈信息
    // 如果第一条消息缺少反馈信息，尝试从conversationGroups中获取
    if (messages.length > 0) {
        const firstMessage = messages[0];
        const hasGoodCaseFeedback = firstMessage.hasOwnProperty('goodCaseFeedback');

        if (!hasGoodCaseFeedback) {

            // 查找对应的对话对象
            let conversationObj = null;
            for (const group of conversationGroups.value) {
                conversationObj = group.conversations.find(conv => conv.id === conversationId);
                if (conversationObj) {
                    break;
                }
            }

            // 如果找到对话对象，将反馈信息添加到第一条消息中
            if (conversationObj) {
                messages[0] = {
                    ...messages[0],
                    isGoodCase: conversationObj.isGoodCase || false,
                    isBadCase: conversationObj.isBadCase || false,
                    goodCaseFeedback: conversationObj.goodCaseFeedback || null,
                    badCaseFeedback: conversationObj.badCaseFeedback || null
                };
            }
        }
    }

    return messages;
};

// 添加新会话到历史记录
const addNewConversation = (conversationId, userMessageData) => {
    if (!conversationId || !userMessageData) return;

    // 创建新会话对象
    const timestamp = Date.now();
    const date = new Date().toISOString().split('T')[0];

    // 支持传入字符串（向后兼容）或消息对象
    const messageContent = typeof userMessageData === 'string' ? userMessageData : userMessageData.content;
    const messageImages = typeof userMessageData === 'object' ? userMessageData.images : [];

    // 创建会话标题（使用用户消息的前30个字符）
    let title = messageContent;
    if (title.length > 30) {
        title = title.substring(0, 30) + '...';
    }

    // 创建会话对象
    const newConversation = {
        id: conversationId,
        title: title,
        date: date,
        timestamp: timestamp,
        active: true
    };

    // 创建消息对象，包含图片信息
    const userMessageObj = {
        id: `user-${timestamp}`,
        role: 'user',
        content: messageContent,
        timestamp: timestamp,
        // 保存图片信息以支持新对话中的图片显示
        images: messageImages || [],
        resource_url: messageImages && messageImages.length > 0 ? messageImages.join(',') : null
    };

    // 添加到原始历史数据
    rawHistoryData.value[conversationId] = [userMessageObj];

    // 将新会话添加到今天的分组
    let todayGroup = conversationGroups.value.find(group => group.id === 'today');

    if (!todayGroup) {
        // 如果今天的分组不存在，创建一个
        todayGroup = {
            id: 'today',
            title: '今天',
            conversations: []
        };
        conversationGroups.value.unshift(todayGroup);
    }

    // 将新会话添加到今天分组的最前面
    todayGroup.conversations.unshift(newConversation);

    // 设置为活动会话
    activeConversationId.value = conversationId;

    // 更新所有会话的活动状态
    conversationGroups.value.forEach(group => {
        group.conversations.forEach(conv => {
            conv.active = conv.id === conversationId;
        });
    });

    return conversationId;
};

// 从URL加载对话
// Load conversation from URL
const loadConversationFromUrl = async () => {
    // 检查URL查询参数中是否有会话ID
    const urlParams = new URLSearchParams(window.location.search);
    const chatParam = urlParams.get('chat');

    if (chatParam) {

        try {
            // 直接从API获取单个对话内容，而不是加载所有历史记录
            const messages = await loadSingleConversation(chatParam);

            if (messages && messages.length > 0) {
                console.log(`成功从API加载对话 ${chatParam}，共 ${messages.length} 条消息`);
                // 不需要调用 selectConversation，因为 loadSingleConversation 已经设置了活动对话
            } else {
                // 如果API返回空或出错，则可能是无效的ID，导航到新对话
                console.warn(`会话ID ${chatParam} 未找到或加载失败。`);
                newConversation();
            }
        } catch (error) {
            console.error(`加载对话 ${chatParam} 时出错:`, error);
            // 出错时，尝试回退到旧方法
            console.log(`尝试回退到加载历史记录方法...`);

            // 检查对话是否已加载
            if (!rawHistoryData.value[chatParam]) {
                // 如果未加载，先加载第一页历史记录
                await loadHistory(1);
            }
            // 再次检查，如果存在则选中
            if (rawHistoryData.value[chatParam]) {
                selectConversation(chatParam);
            } else {
                // 如果历史记录加载后仍然找不到，则可能是无效的ID，导航到新对话
                console.warn(`会话ID ${chatParam} 在加载历史记录后未找到。`);
                newConversation();
            }
        }
        return;
    }

    // 如果没有chat参数，确保是新对话状态
    if (activeConversationId.value !== null) {
        newConversation();
    }
};

// 导出 useHistoryState 函数
export function useHistoryState() {
    return {
        // State
        isLoading,
        error,
        conversationGroups,
        activeConversationId,
        hasMorePages,
        deleteConfirmModal,
        rawHistoryData,

        // Methods
        loadHistory,
        loadMoreHistory,
        loadSingleConversation, // 导出新函数 - 直接加载单个对话
        selectConversation,
        openDeleteConfirmModal,
        closeDeleteConfirmModal,
        confirmDeleteConversation,
        newConversation,
        setActiveConversationId, // 导出设置活动对话ID的方法
        getConversationMessages,
        addNewConversation,
        loadConversationFromUrl // 导出URL加载函数
    };
}

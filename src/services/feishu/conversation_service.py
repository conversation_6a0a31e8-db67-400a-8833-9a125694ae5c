"""
飞书对话服务模块
负责管理对话历史记录、所有权验证等对话相关功能
"""
import json
from datetime import datetime
from src.utils.logger import logger
from services.chatbot.history_service import (
    save_user_message,
    save_assistant_message,
    get_conversation_history_as_input_list,
    check_conversation_ownership,
)
from src.services.feishu.message_apis import reply_simple_text_message


class ConversationService:
    """对话服务类"""
    
    @staticmethod
    async def validate_conversation_ownership(
        user_name: str, user_email: str, message_id: str, conversation_id: str
    ) -> tuple[bool, bool]:
        """验证对话所有权
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            message_id: 消息ID
            conversation_id: 对话ID
            
        Returns:
            bool: 如果用户有权限访问对话返回True，否则返回False
            bool: 如果对话存在返回True，否则返回False
        """
        is_exists = False
        is_owner = False
        if message_id != conversation_id:
            # 检查对话所有权
            is_owner, is_exists = check_conversation_ownership(user_name, user_email, conversation_id)
            if not is_owner and is_exists:
                logger.warning(
                    f"用户 {user_name} ({user_email}) 尝试回复不属于他的对话 {conversation_id}"
                )
                reply_simple_text_message(
                    message_id,
                    "抱歉，您不能回复其他人的对话。请开始一个新的对话或回复您自己的对话。",
                )
        return is_owner, is_exists
    
    @staticmethod
    async def save_user_message_to_history(
        user_name: str, user_email: str, conversation_id: str, user_query: str
    ) -> bool:
        """保存用户消息到历史记录

        Args:
            user_name: 用户名
            user_email: 用户邮箱
            conversation_id: 对话ID
            user_query: 用户查询内容

        Returns:
            bool: 保存成功返回True，失败返回False
        """
        try:
            # 验证必要参数
            if not user_name or not user_email or not conversation_id:
                logger.error(f"保存用户消息失败：缺少必要参数 - user_name={user_name}, user_email={user_email}, conversation_id={conversation_id}")
                return False

            if not user_email or "@" not in user_email or user_email == "unknown":
                logger.error(f"保存用户消息失败：无效的邮箱地址 - user_email={user_email}, user_name={user_name}")
                return False

            success = save_user_message(
                username=user_name,
                email=user_email,
                conversation_id=conversation_id,
                content=user_query,
                timestamp=int(datetime.now().timestamp() * 1000),
            )

            if success:
                logger.info(f"用户消息已成功保存到对话 {conversation_id} - user={user_name}, email={user_email}")
                return True
            else:
                logger.error(f"用户消息保存失败 - user={user_name}, email={user_email}, conversation_id={conversation_id}")
                return False

        except Exception as e:
            logger.error(f"保存用户消息时发生异常: {e} - user={user_name}, email={user_email}, conversation_id={conversation_id}", exc_info=True)
            return False
    
    @staticmethod
    async def get_conversation_history(
        user_name: str, user_email: str, conversation_id: str, root_id: str
    ) -> list:
        """获取对话历史记录
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            conversation_id: 对话ID
            root_id: 根消息ID
            
        Returns:
            list: 格式化后的历史消息列表
        """
        history = []
        if root_id:
            try:
                # 使用新的 service 函数获取格式化后的历史记录
                history = get_conversation_history_as_input_list(
                    user_name, user_email, conversation_id
                )
                logger.info(
                    f"通过 history_service 获取到 {len(history)} 条格式化后的历史消息。"
                )
            except Exception as e:
                logger.error(
                    f"通过 history_service 获取历史对话记录失败: {e}", exc_info=True
                )
                # 如果获取历史失败，继续处理但不使用历史记录
                history = []
        else:
            logger.info("No root_id provided, starting with empty history.")
            history = []
        
        return history
    
    @staticmethod
    async def pre_create_assistant_entry(
        user_name: str,
        user_email: str,
        conversation_id: str,
        temporary_content: str = "🤖 正在思考中，请稍候..."
    ) -> int:
        """预创建助手消息记录（用于卡片初始化时使用chat_history_id）
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            conversation_id: 对话ID
            temporary_content: 临时内容
            
        Returns:
            int: 创建的chat_history_id，如果创建失败则返回None
        """
        try:
            from services.chatbot.history_service import save_assistant_message
            
            # 验证必要参数
            if not user_name or not user_email or not conversation_id:
                logger.error(f"预创建助手消息失败：缺少必要参数")
                return None

            chat_history_id = save_assistant_message(
                username=user_name,
                email=user_email,
                conversation_id=conversation_id,
                content=temporary_content,
                timestamp=int(datetime.now().timestamp() * 1000),
                logs=None,
                output_as_input=None,
                agent=None
            )
            
            if chat_history_id:
                logger.info(f"助手消息记录预创建成功，chat_history_id: {chat_history_id}")
                return chat_history_id
            else:
                logger.error(f"助手消息记录预创建失败")
                return None

        except Exception as e:
            logger.error(f"预创建助手消息时发生异常: {e}", exc_info=True)
            return None

    @staticmethod
    async def save_assistant_response_to_history(
        user_name: str,
        user_email: str,
        conversation_id: str,
        full_response: str,
        full_log_message: str,
        structured_assistant_message: dict = None,
        used_agents: list = None,
        chat_history_id: int = None,
    ):
        """保存助手回复到历史记录
        
        Args:
            user_name: 用户名
            user_email: 用户邮箱
            conversation_id: 对话ID
            full_response: 完整的响应内容
            full_log_message: 完整的日志消息
            structured_assistant_message: 结构化的助手消息
        """
        output_as_input_json = None
        
        # 尝试序列化结构化消息用于 output_as_input 字段
        if structured_assistant_message and isinstance(structured_assistant_message, dict):
            try:
                output_as_input_json = json.dumps(
                    structured_assistant_message, ensure_ascii=False, indent=2
                )
            except Exception as json_err:
                logger.error(
                    f"无法将 structured_assistant_message 序列化为 JSON: {json_err}"
                )
                # 序列化失败，output_as_input_json 保持 None
        
        # 处理agent信息
        agent_str = None
        if used_agents and len(used_agents) > 0:
            agent_str = ",".join(used_agents)
            logger.info(f"保存助手消息，使用的agent: {agent_str}")

        # 如果提供了chat_history_id，则更新现有记录；否则创建新记录
        if chat_history_id:
            from services.chatbot.history_service import update_streaming_assistant_message_content
            success = update_streaming_assistant_message_content(
                message_id=chat_history_id,
                content=full_response,
                logs=full_log_message,
                output_as_input=output_as_input_json,
                agent=agent_str,
                is_completed=True
            )
            if success:
                logger.info(f"助手消息已更新到现有记录 {chat_history_id}")
            else:
                logger.error(f"更新助手消息记录失败 {chat_history_id}")
        else:
            # 调用 save_assistant_message，分别传递 stream logs 和 structured output JSON
            save_assistant_message(
                username=user_name,
                email=user_email,
                conversation_id=conversation_id,
                content=full_response,  # content 始终是流式文本响应
                timestamp=int(datetime.now().timestamp() * 1000),
                logs=full_log_message,  # 传递增强后的日志（包含Tool Agent日志）
                output_as_input=output_as_input_json,  # 传递序列化的结构化消息到 output_as_input 字段
                agent=agent_str  # 传递agent信息化后的结构化输出（或 None）到新字段
            )
        # 更新日志消息以反映保存状态
        logger.info(
            f"助手回复已保存到对话 {conversation_id} (结构化输出状态: {'已保存' if output_as_input_json else '未保存或失败'})"
        )
